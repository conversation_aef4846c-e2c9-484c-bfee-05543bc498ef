Locales = Locales or {}

Locales['es'] = {
  yes = "Si",
  no = "No",
  garage = "Garaje",
  jobGarage = "Garaje Laboral",
  gangGarage = "Garaje de Banda/Pandilla",
  player = "Jugador",
  impound = "Deposito",
  inGarage = "En Garaje",
  notInGarage = "No en Garaje",
  car = "Coche",
  air = "Aereo",
  sea = "Maritimo",
  fuel = "Combustible",
  engine = "Motor",
  body = "Carroceria",
  day = "dia",
  days = "dias",
  hour = "hora",
  hours = "horas",

  -- User Interface
  noVehicles = "No hay vehículos en este garaje.",
  vehicles = "Vehículo(s)",
  vehiclePlate = "Matrícula del Vehículo",
  vehicleNotInGarage = "Vehículo no en garaje",
  vehicleTakeOut = "Conducir",
  vehicleReturnAndTakeOut = "Devuelve y Conduce",
  vehicleReturnToOwnersGarage = "Devolver al garaje del propietario",
  transferToGarageOrPlayer = "Transferir a Garaje o Jugador",
  transferToGarage = "Transferir a un Garaje",
  transferToPlayer = "Transferir a un Jugador",
  vehicleTransfer = "Transferir",
  noAvailableGarages = "No hay garajes disponibles",
  currentGarage = "Garaje Actual",
  noPlayersOnline = "No hay jugadores en línea",
  createPrivateGarage = "Crear Garaje Privado",
  pgAlertHeadsUp = "Advertencia!",
  pgAlertText = "Se creará el garaje y los vehículos aparecerán en la ubicación y dirección exactas en las que se encuentra actualmente.",
  garageName = "Nombre del Garaje",
  impoundInformation = "Información de Incautacion",
  impoundedBy = "Incautado por ",
  impoundedReason = "Razón",
  impoundPlayerCanCollect = "Puedes recoger tu vehículo en el depósito.",
  impoundCollectionContact = "Haga el favor de contactar con %{value} para poder reclamar su vehiculo.",
  impoundNoVehicles = "No hay vehículos en el depósito.",
  impoundAvailable = "Disponible",
  impoundRetrievableByOwner = "Recuperable por el propietario",
  impoundNoReason = "No se proporcionó ninguna razón",
  impoundVehicle = "Incautar Vehiculo",
  impoundReasonField = "Razon (opcional)",
  impoundTime = "Tiempo de Incautacion",
  impoundAvailableImmediately = "Disponible inmediatamente",
  impoundCost = "Coste",
  changeVehiclePlate = "Cambio de Matricula",
  newPlate = "Matricula Nueva",
  search = "Buscar por nombre o placa",
  noPrivateGarages = "Sin garajes privados",
  editPrivateGarage = "Editar Garaje Privado",
  owners = "Propietario(s)",
  location = "Ubicación",
  next = "Siguiente",
  previous = "Anterior",
  page = "Página",
  of = "de",
  show = "Mostrar",
  save = "Guardar",
  edit = "Editar",
  delete = "Borrar",
  garageDeleteConfirm = "¿Estás seguro de que quieres borrar este garaje?",
  privGarageSearch = "Buscar por nombre",
  garageUpdatedSuccess = "¡Garaje actualizado con éxito!",
  getCurrentCoords = "Obtener coordenadas actuales",
  identifier = "Identificador",
  name = "Nombre",
  noPlayers = "No hay jugadores añadidos",
  addPlayer = "Añadir jugador",
  loadingVehicle = "Cargando vehículo...",
  vehicleSetup = "Configuración del vehículo",
  extras = "Extras",
  extra = "Extra",
  liveries = "Diseños",
  livery = "Vinilo",
  noLiveries = "No hay vinilos disponibles",
  noExtras = "No hay extras disponibles",
  none = "Ninguno",
  vehicleNeedsService = "Needs Service",
  type = "Type",

  -- Notifications
  insertVehicleTypeError = "Solo puedes almacenar vehículos de tipo %{value} en este garaje",
  insertVehiclePublicError = "No puede almacenar vehículos de trabajo o de banda en garajes públicos",
  vehicleParkedSuccess = "Vehículo estacionado en garaje",
  vehicleNotOwnedError = "No eres dueño de este vehículo",
  vehicleNotOwnedByPlayerError = "El vehículo no es propiedad de un jugador.",
  notEnoughMoneyError = "No tienes suficiente dinero en tu banco",
  vehicleNotYoursError = "El vehículo no te pertenece",
  notJobOrGangVehicle = "Este no es un vehículo de %{value}",
  invalidGangError = "No ha proporcionado una banda válida",
  invalidJobError = "No ha proporcionado un trabajo válido",
  notInsideVehicleError = "No estás sentado en un vehículo.",
  vehicleAddedToGangGarageSuccess = "Vehículo agregado al garaje de banda de %{value}!",
  vehicleAddedToJobGarageSuccess = "Vehículo agregado al taller de trabajo de %{value}!",
  moveCloserToVehicleError = "Necesitas acercarte al vehículo.",
  noVehiclesNearbyError = "No hay vehículos cerca.",
  commandPermissionsError = "No tienes permiso para usar este comando.",
  actionNotAllowedError = "No tienes permitido hacer esto.",
  garageNameExistsError = "El nombre del garaje ya existe",
  vehiclePlateExistsError = "La placa del vehículo ya está en uso",
  playerNotOnlineError = "El jugador no está en línea",
  vehicleTransferSuccess = "Vehículo transferido a %{value}",
  vehicleTransferSuccessGeneral = "Vehículo transferido con éxito",
  vehicleReceived = "Recibiste un vehículo con matricula %{value}",
  vehicleImpoundSuccess = "Vehículo incautado con éxito",
  vehicleImpoundRemoveSuccess = "Vehículo retirado del depósito",
  vehicleImpoundReturnedToOwnerSuccess = "Vehículo devuelto al garaje del propietario.",
  garageCreatedSuccess = "¡Garaje creado con éxito!",
  vehiclePlateUpdateSuccess = "Matrícula del vehículo establecida a %{value}",
  vehicleDeletedSuccess = "Vehículo eliminado de la base de datos %{value}",
  playerIsDead = "No puedes hacer esto mientras estás muerto",

  -- Commands
  cmdSetGangVehicle = "Agregar el vehículo actual a un garaje de banda",
  cmdRemoveGangVehicle = "Devolver el vehículo de la banda a propiedad del jugador",
  cmdSetJobVehicle = "Agregar vehículo actual a un taller de trabajo",
  cmdRemoveJobVehicle = "Devolver el vehículo de trabajo a propiedad del jugador",
  cmdArgGangName = "Nombre de Banda",
  cmdArgJobName = "Nombre de Trabajo",
  cmdArgPlayerId = "ID de Jugador del nuevo propietario",
  cmdImpoundVehicle = "Incautar un Vehículo",
  cmdChangePlate = "Cambiar Matricula del Vehiculo (Solo Administracion)",
  cmdDeleteVeh = "Eliminar vehículo de la base de datos (Solo Administracion)",
  cmdCreatePrivGarage = "Crea un garaje privado para un jugador.",

  -- v3
  vehicleStoreError = "You cannot store this vehicle here",
  mins = "mins",
  noVehiclesAvailableToDrive = "There are no vehicles available to drive",
}
