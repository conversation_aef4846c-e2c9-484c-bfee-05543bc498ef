-----------------------------------------------------------------------------------
-- 等等！在手动编辑此文件之前，请尝试我们新的简易配置工具！ --
--            https://configurator.jgscripts.com/advanced-garages                --
-----------------------------------------------------------------------------------
Config = {}

-- 本地化设置
Config.Locale = "cn"
Config.NumberAndDateFormat = "en-US"
Config.Currency = "USD"

-- 框架和集成
Config.Framework = "auto" -- 或 "QBCore", "Qbox", "ESX"
Config.FuelSystem = "ox_fuel" -- 或 "LegacyFuel", "ps-fuel", "lj-fuel", "ox_fuel", "cdn-fuel", "hyon_gas_station", "okokGasStation", "nd_fuel", "myFuel", "ti_fuel", "Renewed-Fuel", "rcore_fuel", "none"
Config.VehicleKeys = "auto" -- 或 "qb-vehiclekeys", "MrNewbVehicleKeys", "jaksam-vehicles-keys", "qs-vehiclekeys", "mk_vehiclekeys", "wasabi_carlock", "cd_garage", "okokGarage", "t1ger_keys", "Renewed", "tgiann-hotwire" "none"
Config.Notifications = "auto" -- 或 "default", "okokNotify", "ox_lib", "ps-ui"
Config.Banking = "auto" -- 或 "qb-banking", "qb-management", "esx_addonaccount", "Renewed-Banking", "okokBanking", "fd_banking"
Config.Gangs = "auto" -- "qb-gangs", "rcore_gangs"

-- 绘制文本UI提示 (按键绑定控制ID参考: https://docs.fivem.net/docs/game-references/controls/)
Config.DrawText = "auto" -- 或 "jg-textui", "qb-DrawText", "okokTextUI", "ox_lib", "ps-ui"
Config.OpenGarageKeyBind = 38
Config.OpenGaragePrompt = "[E] 打开车库"
Config.OpenImpoundKeyBind = 38
Config.OpenImpoundPrompt = "[E] 打开扣押场"
Config.InsertVehicleKeyBind = 38
Config.InsertVehiclePrompt = "[E] 保存车辆"
Config.ExitInteriorKeyBind = 38
Config.ExitInteriorPrompt = "[E] 退出车库"

-- 目标系统
Config.UseTarget = false
Config.Target = "ox_target" -- 或 "qb-target"
Config.TargetPed = "s_m_y_valet_01"

-- 径向菜单
Config.UseRadialMenu = false
Config.RadialMenu = "ox_lib"


-- 车库UI中的小型车辆预览图像 - 了解更多/添加自定义图像: https://docs.jgscripts.com/advanced-garages/vehicle-images
Config.ShowVehicleImages = true

-- 车辆生成和存储
Config.DoNotSpawnInsideVehicle = false
Config.SaveVehicleDamage = true -- 从车库取出车辆时保存并应用车身和引擎损坏
Config.AdvancedVehicleDamage = true -- 使用 Kiminaze 的 VehicleDeformation
Config.SaveVehiclePropsOnInsert = true
Config.CheckVehicleModel = true -- 额外安全检查

-- 如果你不知道这意味着什么，请不要修改
-- 如果你知道这意味着什么，我建议启用它，但请注意在人口较多的服务器上可能会遇到可靠性问题
-- 遇到重大问题？我恳求你在向我们开票之前将其设置回false
-- 强烈建议如果你决定启用此功能，请设置 Config.DoNotSpawnInsideVehicle = false
-- 想要阅读我关于服务器生成车辆的咆哮？ https://docs.jgscripts.com/advanced-garages/misc/why-are-you-not-using-createvehicleserversetter-by-default
Config.SpawnVehiclesWithServerSetter = false

-- 车辆转移
Config.GarageVehicleTransferCost = 2500 -- 车库间转移费用
Config.TransferHidePlayerNames = false
Config.EnableTransfers = {
  betweenGarages = true,
  betweenPlayers = true
}
Config.DisableTransfersToUnregisteredGarages = false -- 对警惕服务器的潜在黑客保护 - 未注册的车库是通过第三方脚本集成中的事件创建的，如房屋脚本，因此可能容易受到脚本小子攻击。

-- 防止车辆复制
-- 了解更多: https://docs.jgscripts.com/advanced-garages/vehicle-duplication-prevention
Config.AllowInfiniteVehicleSpawns = false -- 公共和私人车库
Config.JobGaragesAllowInfiniteVehicleSpawns = false -- 工作车库
Config.GangGaragesAllowInfiniteVehicleSpawns = false -- 帮派车库
Config.GarageVehicleReturnCost = 2500 -- 如果服务器重启后未放回车库或被摧毁或在水下时的"拖车"税
Config.GarageVehicleReturnCostSocietyFund = false -- 支付返还费用的社会基金工作名称（可选）

-- 公共车库
Config.GarageShowBlips = true
Config.GarageUniqueBlips = false
Config.GarageUniqueLocations = true
Config.GarageEnableInteriors = true
Config.GarageLocations = { -- 重要 - 每个车库名称必须是唯一的
  ["军团广场"] = { -- 如果你将此车库名称从军团广场更改，你必须在SQL表`players_vehicles`中将`garage_id`的默认值更改为相同名称
    coords = vector3(215.09, -805.17, 30.81),
    spawn = {vector4(216.84, -802.02, 30.78, 69.82), vector4(218.09, -799.42, 30.76, 66.17), vector4(219.29, -797.23, 30.75, 65.4), vector4(219.59, -794.44, 30.75, 69.35), vector4(220.63, -792.03, 30.75, 63.76), vector4(206.81, -798.35, 30.99, 248.53)}, -- 你可以将多个生成位置添加到表中
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["伊斯灵顿南"] = {
    coords = vector3(273.0, -343.85, 44.91),
    spawn = vector4(270.75, -340.51, 44.92, 342.03),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["格罗夫街"] = {
    coords = vector3(14.66, -1728.52, 29.3),
    spawn = vector4(23.93, -1722.9, 29.3, 310.58),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["镜子公园"] = {
    coords = vector3(1032.84, -765.1, 58.18),
    spawn = vector4(1023.2, -764.27, 57.96, 319.66),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["海滩"] = {
    coords = vector3(-1248.69, -1425.71, 4.32),
    spawn = vector4(-1244.27, -1422.08, 4.32, 37.12),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["大洋公路"] = {
    coords = vector3(-2961.58, 375.93, 15.02),
    spawn = vector4(-2964.96, 372.07, 14.78, 86.07),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["沙滩南"] = {
    coords = vector3(217.33, 2605.65, 46.04),
    spawn = vector4(216.94, 2608.44, 46.33, 14.07),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["沙滩北"] = {
    coords = vector3(1878.44, 3760.1, 32.94),
    spawn = vector4(1880.14, 3757.73, 32.93, 215.54),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["北维恩伍德大道"] = {
    coords = vector3(365.21, 295.65, 103.46),
    spawn = vector4(364.84, 289.73, 103.42, 164.23),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["葡萄籽"] = {
    coords = vector3(1713.06, 4745.32, 41.96),
    spawn = vector4(1710.64, 4746.94, 41.95, 90.11),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["佩莱托湾"] = {
    coords = vector3(107.32, 6611.77, 31.98),
    spawn = vector4(110.84, 6607.82, 31.86, 265.28),
    distance = 15,
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["船只"] = {
    coords = vector3(-795.15, -1510.79, 1.6),
    spawn = vector4(-798.66, -1507.73, -0.47, 102.23),
    distance = 20,
    type = "sea",
    hideBlip = false,
    blip = {
      id = 356,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["机库"] = {
    coords = vector3(-1243.49, -3391.88, 13.94),
    spawn = vector4(-1258.4, -3394.56, 13.94, 328.23),
    distance = 20,
    type = "air",
    hideBlip = false,
    blip = {
      id = 359,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  }
}

-- 私人车库
Config.PrivGarageCreateCommand = "privategarages"
Config.PrivGarageCreateJobRestriction = {"realestate"}
Config.PrivGarageEnableInteriors = true
Config.PrivGarageHideBlips = false
Config.PrivGarageBlip = {
  id = 357,
  color = 0,
  scale = 0.7
}

-- 工作车库
Config.JobGarageShowBlips = true
Config.JobGarageSetVehicleCommand = "setjobvehicle" -- 仅管理员
Config.JobGarageRemoveVehicleCommand = "removejobvehicle" -- 仅管理员
Config.JobGarageUniqueBlips = false
Config.JobGarageUniqueLocations = true
Config.JobGarageEnableInteriors = true
Config.JobGarageLocations = { -- 重要 - 每个车库名称必须是唯一的
  ["修理工"] = {
    coords = vector3(157.86, -3005.9, 7.03),
    spawn = vector4(165.26, -3014.94, 5.9, 268.8),
    distance = 15,
    job = {"mechanic"},
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
    vehiclesType = "owned", -- 使用此社会中任何人都可以访问的拥有车辆 - 更多详情: https://docs.jgscripts.com/advanced-garages/job-and-gang-garages
  },
  ["警察"] = {
    coords = vector3(595.67, -10.88, 70.63),
    spawn = vector4(585.92, -4.07, 70.63, 62),
    distance = 15,
    job = {"police"},
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
    vehiclesType = "spawner", -- 从列表中生成基础车辆
    showLiveriesExtrasMenu = true, -- 允许玩家在取出车辆前选择涂装和配件
    vehicles = {
      [1] = {
        model = "legacycharg",
        plate = false,
        minJobGrade = 0,
        nickname = "警车1",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [2] = {
        model = "legacycharg2",
        plate = false,
        minJobGrade = 0,
        nickname = "警车2",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [3] = {
        model = "legacycvpi",
        plate = false,
        minJobGrade = 0,
        nickname = "警车3",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [4] = {
        model = "lapd_bmw",
        plate = false,
        minJobGrade = 1,
        nickname = "宝马摩托",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [5] = {
        model = "legacyfpiu",
        plate = false,
        minJobGrade = 0,
        nickname = "警用5",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [6] = {
        model = "legacyf150",
        plate = false,
        minJobGrade = 1,
        nickname = "福特F150",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [7] = {
        model = "bolideyh",
        plate = false,
        minJobGrade = 3,
        nickname = "超跑警用车",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [8] = {
        model = "legacytahoe",
        plate = false,
        minJobGrade = 1,
        nickname = "警用6",
        livery = 1,
        extras = {},
        maxMods = true
      },
      [9] = {
        model = "939amborg",
        plate = false,
        minJobGrade = 4,
        nickname = "兰博基尼改装700",
        livery = 1,
        extras = {},
        maxMods = true
      }
    }
  }
}

-- 帮派车库 (默认仅QBCore/Qbox)
Config.GangEnableCustomESXIntegration = false -- 如果你已在cl/sv-functions.lua中添加自定义系统，请设置为true
Config.GangGarageShowBlips = true
Config.GangGarageSetVehicleCommand = "setgangvehicle" -- 仅管理员
Config.GangGarageRemoveVehicleCommand = "removegangvehicle" -- 仅管理员
Config.GangGarageUniqueBlips = false
Config.GangGarageUniqueLocations = true
Config.GangGarageEnableInteriors = true
Config.GangGarageLocations = { -- 重要 - 每个车库名称必须是唯一的
  ["失落摩托车俱乐部"] = {
    coords = vector3(439.18, -1518.48, 29.28),
    spawn = vector4(439.18, -1518.48, 29.28, 139.06),
    distance = 15,
    gang = {"lostmc"},
    type = "car",
    hideBlip = false,
    blip = {
      id = 357,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
    vehiclesType = "personal", -- 使用个人车辆
  }
}

-- 扣押场
Config.ImpoundCommand = "iv"
Config.ImpoundFeesSocietyFund = "police" -- 支付扣押费用的社会基金工作名称（可选）
Config.ImpoundShowBlips = true
Config.ImpoundUniqueBlips = false
Config.ImpoundTimeOptions = {0, 1, 4, 12, 24, 72, 168} -- 以小时为单位
Config.ImpoundLocations = { -- 重要 - 每个扣押场名称必须是唯一的
  ["扣押场A"] = {
    coords = vector3(410.8, -1626.26, 29.29),
    spawn = vector4(408.44, -1630.88, 29.29, 136.88),
    distance = 15,
    type = "car",
    job = {"police"},
    hideBlip = false,
    blip = {
      id = 68,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  },
  ["扣押场B"] = {
    coords = vector3(1649.71, 3789.61, 34.79),
    spawn = vector4(1643.66, 3798.36, 34.49, 216.16),
    distance = 15,
    type = "car",
    job = {"police"},
    hideBlip = false,
    blip = {
      id = 68,
      color = 0,
      scale = 0.7
    },
    hideMarkers = true,
    markers = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
  }
}

-- 车库内部
Config.GarageInteriorEntrance = vector4(227.96, -1003.06, -99.0, 0.0)
Config.GarageInteriorCameraCutscene = {
  vector4(227.96, -977.81, -98.99, 0.0), -- 从
  vector4(227.96, -1006.96, -98.99, 0.0), -- 到（这应该是入口，或者从入口坐标稍微向后一点以获得更好的最终玩家过渡）
}
Config.GarageInteriorVehiclePositions = {
  vector4(233.000000, -984.000000, -99.410004, 118.000000),
  vector4(233.000000, -988.500000, -99.410004, 118.000000),
  vector4(233.000000, -993.000000, -99.410004, 118.000000),
  vector4(233.000000, -997.500000, -99.410004, 118.000000),
  vector4(233.000000, -1002.000000, -99.410004, 118.000000),
  vector4(223.600006, -979.000000, -99.410004, 235.199997),
  vector4(223.600006, -983.599976, -99.410004, 235.199997),
  vector4(223.600006, -988.200012, -99.410004, 235.199997),
  vector4(223.600006, -992.799988, -99.410004, 235.199997),
  vector4(223.600006, -997.400024, -99.410004, 235.199997),
  vector4(223.600006, -1002.000000, -99.410004, 235.199997),
}

-- 管理员命令
Config.ChangeVehiclePlate = "vplate" -- 仅管理员
Config.DeleteVehicleFromDB = "dvdb" -- 仅管理员
Config.ReturnVehicleToGarage = "vreturn" -- 仅管理员

-- 在此处添加你的导入车辆的生成名称和所需标签，以在车库中显示漂亮的车辆名称
-- 这主要是为ESX设计的 - 如果你使用QB，请在shared中执行此操作！
Config.VehicleLabels = {
  ["spawnName"] = "漂亮的车辆标签"
}

-- 阻止某些车辆被转移给其他玩家
Config.PlayerTransferBlacklist = {
  "spawnName"
}

Config.AutoRunSQL = true
Config.ReturnToPreviousRoutingBucket = false
Config.HideWatermark = false
Config.__v3Config = true
Config.Debug = false