Locales = Locales or {}

Locales['lt'] = {
  yes = "Taip",
  no = "Ne",
  garage = "Garažas",
  jobGarage = "Darbo garažas",
  gangGarage = "Gaujos garažas",
  player = "Asmuo",
  impound = "Konfiskavimo a<PERSON>telė",
  inGarage = "Šiuo metu garaže",
  notInGarage = "Garaže nėra",
  car = "Transporto priemonė",
  air = "Lėktuvas",
  sea = "Laivas",
  fuel = "Degalai",
  engine = "Variklis",
  body = "Kėbulas",
  day = "diena",
  days = "dienos",
  hour = "valanda",
  hours = "valandos",
  mins = "mins",

  -- User Interface
  noVehicles = "Šiame garaže nėra transporto priemonių",
  noVehiclesAvailableToDrive = "Šiuo metu nėra transporto priemonių, kurias galėtumėte vairuoti",
  vehicles = "tr.priemonė-(s)",
  vehiclePlate = "Numeriai",
  vehicleNotInGarage = "Tr.priemonės nėra garaže",
  vehicleTakeOut = "Vairuoti",
  vehicleReturnAndTakeOut = "Gražinti & Vairuoti",
  vehicleReturnToOwnersGarage = "Grąžinti savininkui į garažą",
  transferToGarageOrPlayer = "Perkelti į garažą arba asmeniui",
  transferToGarage = "Perkelti į garažą",
  transferToPlayer = "Perleisti žaidėjui",
  vehicleTransfer = "Perkelti",
  noAvailableGarages = "Nėra galimų garažų",
  currentGarage = "Dabartinis garažas",
  noPlayersOnline = "Nėra prisijungusių žaidėjų",
  createPrivateGarage = "Sukurti privatų garažą",
  pgAlertHeadsUp = "Dėmesio!",
  pgAlertText = "Garažas bus sukurtas, o transporto priemonė atsiras tiklsiai ten, kur dabar stovite.",
  garageName = "Garažo pavadinimas",
  impoundInformation = "Konfiskavimo informacija",
  impoundedBy = "Konfiskavo",
  impoundedReason = "Priežastis",
  impoundPlayerCanCollect = "Savo transporto priemonę galite atsiimti iš konfiskavimo aikštelės.",
  impoundCollectionContact = "Prašome susisiekti su %{value}, kad galėtumėte atsiimti savo transporto priemonę.",
  impoundNoVehicles = "Konfiskavimo aikštelėje nėra transporto priemonių",
  impoundAvailable = "Galima",
  impoundRetrievableByOwner = "Atsiimti gali savininkas?",
  impoundNoReason = "Priežastis nenurodyta",
  impoundVehicle = "Transporto priemonės konfiskavimas",
  impoundReasonField = "Priežastis (nebūtina)",
  impoundTime = "Konfiskavimo laikas",
  impoundAvailableImmediately = "Atsiimti gali iškart",
  impoundCost = "Atsiėmimo kaina",
  changeVehiclePlate = "Numerių keitimas",
  newPlate = "Nauji numeriai",
  search = "Ieškoti pagal pavadinimą arba numerius",
  noPrivateGarages = "Privačių garažų nėra",
  editPrivateGarage = "Redaguoti privatų garažą",
  owners = "Savininkas(-ai)",
  location = "Vietovė",
  next = "Kitas",
  previous = "Praeitas",
  page = "Puslapis",
  of = "iš",
  show = "Rodyti",
  save = "Išsaugoti",
  edit = "Redaguoti",
  delete = "Ištrinti",
  garageDeleteConfirm = "Ar tikrai norite ištrinti garažą?",
  privGarageSearch = "Ieškoti pagal pavadinima",
  garageUpdatedSuccess = "Garažas sėkmingai atnaujintas!",
  getCurrentCoords = "Išgauti koordinates",
  identifier = "Identifikatorius",
  name = "Vardas",
  noPlayers = "Žaidėjų nėra pridėta",
  addPlayer = "Pridėti žaidėją",
  loadingVehicle = "Užkraunama...",
  vehicleSetup = "Papildiniai",
  extras = "Extra",
  extra = "Extra",
  liveries = "Lipdukai",
  livery = "Lipdukas",
  noLiveries = "Lipdukų nėra",
  noExtras = "Extra papildinių nėra",
  none = "Nėra",
  vehicleNeedsService = "Reikia serviso",
  type = "Tipas",
  goInside = "Eiti į vidų",

  -- Notifications
  insertVehicleTypeError = "Šiame garaže galite laikyti tik %{value} transporto priemonių tipus",
  insertVehiclePublicError = "Negalite pastatyti darbo arba gaujų transporto priemonių tarp viešojo garažo",
  vehicleParkedSuccess = "Transporto priemonė pastatyta į garažą",
  vehicleNotOwnedError = "Ši transporto priemonė nepriklauso jums",
  vehicleNotOwnedByPlayerError = "Transporto priemonė nepriklauso jokiam asmeniui",
  notEnoughMoneyError = "Neturite pakankamai pinigų banke",
  vehicleNotYoursError = "Transporto priemonė jums nepriklauso",
  notJobOrGangVehicle = "Tai nėra %{value} transporto priemonė",
  invalidGangError = "Jūs nenurodėte tinkamos gaujos",
  invalidJobError = "Jūs nenurodėte tinkamo darbo",
  notInsideVehicleError = "Jūs nesate transporto priemonėje",
  vehicleAddedToGangGarageSuccess = "Transporto priemonė pridėta į %{value} gaujos garažą!",
  vehicleAddedToJobGarageSuccess = "Transporto priemonė pridėta į %{value} darbo garažą!",
  moveCloserToVehicleError = "Turite prieiti arčiau transporto priemonės",
  noVehiclesNearbyError = "Šalia transporto priemonės nėra",
  commandPermissionsError = "Neturite leidimo naudoti šios komandos",
  actionNotAllowedError = "Šio veiksmo atlikti negalite",
  garageNameExistsError = "Garažo pavadinimas jau yra užimtas",
  vehiclePlateExistsError = "Transporto priemonės numeriai jau yra užimti",
  playerNotOnlineError = "Žaidėjas nėra prisijungęs",
  vehicleTransferSuccess = "Transporto priemonė perkelta į %{value}",
  vehicleTransferSuccessGeneral = "Transporto priemonė sėkmingai perleista",
  vehicleReceived = "Jūs gavote transporto priemonę kurios numeriai yra %{value}",
  vehicleImpoundSuccess = "Transporto priemonė sėkmingai konfiskuota",
  vehicleImpoundRemoveSuccess = "Transporto priemonė buvo ištraukta iš konfiskavimo aikštelės",
  vehicleImpoundReturnedToOwnerSuccess = "Transporto priemonė grąžinta savininkui į garažą",
  garageCreatedSuccess = "Garažas sėkmingai sukurtas!",
  vehiclePlateUpdateSuccess = "Transporto priemonės numeriai pakeisti į %{value}",
  vehicleDeletedSuccess = "Transporto priemonė sėkmingai pašalinta iš duomenu bazės %{value}",
  playerIsDead = "Šio veiksmo negalite atlikti kol esate negyvas",
  vehicleStoreError = "Šios transporto priemonės laikyti čia negalite",

  -- Commands
  cmdSetGangVehicle = "Pridėti dabartinę transporto priemonę į gaujos garažą",
  cmdRemoveGangVehicle = "Grąžinti gaujos transporto priemonę savininkui",
  cmdSetJobVehicle = "Pridėti dabartine transporto priemonę į darbo garažą",
  cmdRemoveJobVehicle = "Gražinti darbinę transporto priemonę savininkui",
  cmdArgGangName = "Gaujos pavadinimas",
  cmdArgJobName = "Darbo pavadinimas",
  cmgArgMinGangRank = "Minimalus gaujos rangas",
  cmgArgMinJobRank = "Minimalus darbo rangas",
  cmdArgPlayerId = "Naujo savininko ID",
  cmdImpoundVehicle = "Konfiskuoti transporto priemonę",
  cmdChangePlate = "Keisit transporto priemonės numerius (Tik administracija)",
  cmdDeleteVeh = "Ištrinti transporto priemonę iš duomenų bazės (Tik administracija)",
  cmdCreatePrivGarage = "Sukurti privatų garažą",
}
