Locales = Locales or {}

Locales['sv'] = {
  yes = "Ja",
  no = "Nej",
  garage = "garage",
  jobGarage = "jobb Garage",
  gangGarage = "gäng Garage",
  player = "spelare",
  impound = "bärgare",
  inGarage = "i garaget",
  notInGarage = "inte i garaget",
  car = "bil",
  air = "luft",
  sea = "vatten",
  fuel = "bränsle",
  engine = "motor",
  body = "chassie",
  day = "dag",
  days = "dagar",
  hour = "timme",
  hours = "timmar",

  -- User Interface
  noVehicles = "Det finns inga fordon i detta garaget",
  vehicles = "bil(ar)",
  vehiclePlate = "Registreringsskylt",
  vehicleNotInGarage = "Fordonet är inte i garaget",
  vehicleTakeOut = "Ta ut",
  vehicleReturnAndTakeOut = "Flytta & Kör",
  vehicleReturnToOwnersGarage = "Lämna tillbaka till ägarens garage",
  transferToGarageOrPlayer = "Flytta till ett garage eller spelare",
  transferToGarage = "Flytta till ett garage",
  transferToPlayer = "Flytta till en spelare",
  vehicleTransfer = "Flytta",
  noAvailableGarages = "Inga tillgängliga garage",
  currentGarage = "Nuvarande garage",
  noPlayersOnline = "Inga spelare online",
  createPrivateGarage = "Skapa privat garage",
  pgAlertHeadsUp = "Akta!",
  pgAlertText = "Garaget skapas och fordon kommer att spawnas på din position och åt det håll du står.",
  garageName = "Garage Namn",
  impoundInformation = "Bärgar Information",
  impoundedBy = "Bärgad av",
  impoundedReason = "Orsak",
  impoundPlayerCanCollect = "Du kan hämta din bil från bärgaren.",
  impoundCollectionContact = "Kontakta %{value} för att hämta ditt fordon.",
  impoundNoVehicles = "Det finns inga fordon hos bärgaren.",
  impoundAvailable = "Tillgänglig",
  impoundRetrievableByOwner = "Kan hämtas av ägaren",
  impoundNoReason = "Ingen anledning tillgänglig",
  impoundVehicle = "Bärgat fordon",
  impoundReasonField = "Orsak (valfritt)",
  impoundTime = "Tid hos bärgaren",
  impoundAvailableImmediately = "Tillgänglig direkt",
  impoundCost = "Kostnad",
  changeVehiclePlate = "Ändra registreringsnummer",
  newPlate = "Nytt registreringsnummer",
  search = "Sök med namn eller registreringsskylt",
  noPrivateGarages = "Inga privata garage",
  editPrivateGarage = "Ändra privat garage",
  owners = "Ägare(n)",
  location = "Plats",
  next = "Nästa",
  previous = "Tidigare",
  page = "Sida",
  of = "av",
  show = "Visa",
  save = "Spara",
  edit = "Redigera",
  delete = "Radera",
  garageDeleteConfirm = "Är du säker på att du vill ta bort detta garage?",
  privGarageSearch = "Sök efter namn",
  garageUpdatedSuccess = "Garaget har uppdaterats framgångsrikt!",
  getCurrentCoords = "Hämta aktuella koordinater",
  identifier = "Identifierare",
  name = "Namn",
  noPlayers = "Inga spelare har tillkommit",
  addPlayer = "Lägg till spelare",
  loadingVehicle = "Lastning av fordon...",
  vehicleSetup = "Inställning av fordon",
  extras = "Extramaterial",
  extra = "Extra",
  liveries = "Liveries",
  livery = "livery",
  noLiveries = "Inga liveries tillgängliga",
  noExtras = "Inga extrafunktioner tillgängliga",
  none = "Ingen",
  vehicleNeedsService = "Dags för service",
  type = "Typ",

  -- Notifications
  insertVehicleTypeError = "Du kan endast förvara %{value} fordonstyp i detta garage",
  insertVehiclePublicError = "Du kan inte förvara Gäng eller Jobbfordon i allmänna garaget",
  vehicleParkedSuccess = "Fordonet är parkerat i garaget",
  vehicleNotOwnedError = "Du äger inte detta fordon",
  vehicleNotOwnedByPlayerError = "Fordonet ägs inte av en spelare",
  notEnoughMoneyError = "Du har inte nog med pengar på banken",
  vehicleNotYoursError = "Fordonet tillhör inte dig",
  notJobOrGangVehicle = "Detta är inte ett %{value} fordon",
  invalidGangError = "Du har inte angett ett giltigt gäng",
  invalidJobError = "Du har inte angett ett giltigt jobb",
  notInsideVehicleError = "Du sitter inte i fordonet",
  vehicleAddedToGangGarageSuccess = "Fordonet är inlagt i %{value} Gäng garage!",
  vehicleAddedToJobGarageSuccess = "Fordonet är inlagt %{value} jobb garage!",
  moveCloserToVehicleError = "Du måste vara närmare fordonet",
  noVehiclesNearbyError = "Det finns inga fordon i närheten",
  commandPermissionsError = "Du får inte använda detta kommando",
  actionNotAllowedError = "Du får inte göra detta",
  garageNameExistsError = "Garagenamnet existerar redan",
  vehiclePlateExistsError = "Registreringsnummer finns redan",
  playerNotOnlineError = "Spelaren är inte online",
  vehicleTransferSuccess = "Fordonet fördes över till %{value}",
  vehicleTransferSuccessGeneral = "Fordonet har överförts",
  vehicleReceived = "Du fick ett fordon med skylten %{value}",
  vehicleImpoundSuccess = "Fordonet är bärgad",
  vehicleImpoundRemoveSuccess = "Fordonet hämtad från bärgaren",
  vehicleImpoundReturnedToOwnerSuccess = "Fordonet återlämnades till ägarens garage",
  garageCreatedSuccess = "Garage har skapats!",
  vehiclePlateUpdateSuccess = "Fordonsskylt är uppdaterad till %{value}",
  vehicleDeletedSuccess = "Fordonet raderat från databasen %{value}",
  playerIsDead = "Du kan inte göra detta då du är död",

  -- Commands
  cmdSetGangVehicle = "Lägg till nuvarande fordon till ett gänggarage",
  cmdRemoveGangVehicle = "Sätt tillbaka gängfordonet till spelarägt",
  cmdSetJobVehicle = "Lägg till aktuellt fordon till ett jobbgarage",
  cmdRemoveJobVehicle = "Sätt tillbaka Jobbfordonet till spelarägt",
  cmdArgGangName = "Gängnamn",
  cmdArgJobName = "Jobbnamn",
  cmgArgMinGangRank = "Minimum gäng rank",
  cmgArgMinJobRank = "Minimum jobb rank",
  cmdArgPlayerId = "SpelarID för nya ägare",
  cmdImpoundVehicle = "Bärga ett fordon",
  cmdChangePlate = "Ändra fordonsskylt (Admin endast)",
  cmdDeleteVeh = "Ta bort fordon från databasen (Admin endast)",
  cmdCreatePrivGarage = "Gör ett privatgarage till en spelare",

  -- v3
  vehicleStoreError = "Du kan inte ställa in detta fordonet här",
  mins = "minuter",
  noVehiclesAvailableToDrive = "Det finns inga fordon i detta garaget",
}
