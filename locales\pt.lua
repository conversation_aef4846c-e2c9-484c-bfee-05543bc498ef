Locales = Locales or {}

Locales['pt'] = {
  yes = "Sim",
  no = "Não",
  garage = "Garagem",
  jobGarage = "Garagem de Trabalho",
  gangGarage = "Garagem de Organização",
  player = "Jogador",
  impound = "Apreendidos",
  inGarage = "Na Garagem",
  notInGarage = "Fora da Garagem",
  car = "Garagem",
  air = "Garagem",
  sea = "Garagem",
  fuel = "Combustível",
  engine = "Motor",
  body = "Chassi",
  day = "Dia",
  days = "Dias",
  hour = "Hora",
  hours = "Horas",

  -- User Interface
  noVehicles = "Não há veículos nesta garagem",
  vehicles = "Total de Viaturas",
  vehiclePlate = "Matrícula do veículo",
  vehicleNotInGarage = "Veículo não está na garagem",
  vehicleTakeOut = "Retirar",
  vehicleReturnAndTakeOut = "Voltar a Retirar",
  vehicleReturnToOwnersGarage = "Voltar à garagem do proprietário",
  transferToGarageOrPlayer = "Transferir para uma Garagem ou Jogador",
  transferToGarage = "Transferir para uma Garagem",
  transferToPlayer = "Transferir para um Jogador",
  vehicleTransfer = "Transferir",
  noAvailableGarages = "Sem garagens disponíveis",
  currentGarage = "Garagem atual",
  noPlayersOnline = "Sem jogadores online",
  createPrivateGarage = "Criar Garagem Privada",
  pgAlertHeadsUp = "Atenção!",
  pgAlertText = "A garagem será criada e os veículos aparecerão na localização e direção exatas que você está atualmente.",
  garageName = "Nome da Garagem",
  impoundInformation = "Informações da Apreensão",
  impoundedBy = "Apreendido por",
  impoundedReason = "Razão",
  impoundPlayerCanCollect = "Você pode retirar o seu veículo dos Apreendidos.",
  impoundCollectionContact = "Por favor entre em contato %{value} Para retirar o seu veículo.",
  impoundNoVehicles = "Não há veículos nos Apreendidos",
  impoundAvailable = "Disponível",
  impoundRetrievableByOwner = "Recuperável pelo proprietário",
  impoundNoReason = "Nenhuma razão fornecida",
  impoundVehicle = "Apreender o Veículo",
  impoundReasonField = "Razão",
  impoundTime = "Tempo de Apreensão",
  impoundAvailableImmediately = "Disponível imediatamente",
  impoundCost = "Preço",
  changeVehiclePlate = "Mudar a Matrícula",
  newPlate = "Nova Matrícula",
  search = "Pesquise por nome ou placa",
  noPrivateGarages = "Sem garagens privadas",
  editPrivateGarage = "Editar garagem privada",
  owners = "Proprietário(s)",
  location = "Localização",
  next = "Seguinte",
  previous = "Anterior",
  page = "Página",
  of = "de",
  show = "Mostrar",
  save = "Guardar",
  edit = "Editar",
  delete = "Eliminar",
  garageDeleteConfirm = "Tem a certeza de que pretende apagar esta garagem?",
  privGarageSearch = "Procurar por nome",
  garageUpdatedSuccess = "Garagem actualizada com sucesso!",
  getCurrentCoords = "Obter as coordenadas actuais",
  identifier = "Identificador",
  name = "Nome",
  noPlayers = "Não há jogadores adicionados",
  addPlayer = "Adicionar jogador",
  loadingVehicle = "Carregamento do veículo...",
  vehicleSetup = "Configuração do veículo",
  extras = "Suplementos",
  extra = "Suplemento",
  liveries = "Fígados",
  livery = "Fígado",
  noLiveries = "Não existem pinturas disponíveis",
  noExtras = "Não há extras disponíveis",
  none = "Nenhum",
  vehicleNeedsService = "Needs Service",
  type = "Type",

  -- Notifications
  insertVehicleTypeError = "Não pode guardar nesta garagem.",
  insertVehiclePublicError = "Você não pode armazenar veículos de emprego ou gangues em garagens públicas",
  vehicleParkedSuccess = "Veículo guardado na garagem",
  vehicleNotOwnedError = "Você não é dono este veículo",
  vehicleNotOwnedByPlayerError = "O veículo não é de propriedade de um jogador",
  notEnoughMoneyError = "Você não tem dinheiro suficiente em seu banco",
  vehicleNotYoursError = "Veículo não pertence a você",
  notJobOrGangVehicle = "Isso não é um %{value} veículo",
  invalidGangError = "Você não forneceu uma gangue válida",
  invalidJobError = "Você não forneceu um trabalho válido",
  notInsideVehicleError = "Você não está sentado em um veículo",
  vehicleAddedToGangGarageSuccess = "Veículo adicionado ao %{value} Garagem de gangues!",
  vehicleAddedToJobGarageSuccess = "Veículo adicionado ao %{value} Jó Garage!",
  moveCloserToVehicleError = "Você precisa se aproximar do veículo",
  noVehiclesNearbyError = "Não há veículos próximos",
  commandPermissionsError = "Você não tem permissão para usar este comando",
  actionNotAllowedError = "Você não tem permissão para fazer isso",
  garageNameExistsError = "O nome da garagem já existe",
  vehiclePlateExistsError = "A placa do veículo já está em uso",
  playerNotOnlineError = "Jogador não está online",
  vehicleTransferSuccess = "Veículo transferido para %{value}",
  vehicleTransferSuccessGeneral = "Veículo transferido com sucesso",
  vehicleReceived = "Você recebeu um veículo com a matricula %{value}",
  vehicleImpoundSuccess = "Veículo foi apreendido",
  vehicleImpoundRemoveSuccess = "Veículo foi removido dos Apreenndidos!",
  vehicleImpoundReturnedToOwnerSuccess = "Veículo voltou à garagem do proprietário",
  garageCreatedSuccess = "Garagem criada com sucesso!",
  vehiclePlateUpdateSuccess = "Placa do veículo definido para %{value}",
  vehicleDeletedSuccess = "Veículo excluído do banco de dados %{value}",
  playerIsDead = "Você não pode fazer isso enquanto estiver morto",

  -- Commands
  cmdSetGangVehicle = "Adicione o veículo atual a uma garagem de gangue",
  cmdRemoveGangVehicle = "Defina o veículo de gangue de volta ao jogador de propriedade",
  cmdSetJobVehicle = "Adicione o veículo atual a uma garagem de Trabalho",
  cmdRemoveJobVehicle = "Defina o veículo de trabalho de volta para o jogador de propriedade",
  cmdArgGangName = "Gang Nome",
  cmdArgJobName = "Trabalho Nome",
  cmdArgPlayerId = "ID do jogador do novo proprietário",
  cmdImpoundVehicle = "Apreender um veículo",
  cmdChangePlate = "Altere a placa do veículo (Admin apenas)",
  cmdDeleteVeh = "Excluir veículo do banco de dados (Admin apenas)",
  cmdCreatePrivGarage = "Criar uma garagem privada para um jogador",

  -- v3
  vehicleStoreError = "You cannot store this vehicle here",
  mins = "mins",
  noVehiclesAvailableToDrive = "There are no vehicles available to drive",
}
