Locales = Locales or {}

Locales['ro'] = {
  yes = "Da",
  no = "Nu",
  garage = "Garaj",
  jobGarage = "Garaj de serviciu",
  gangGarage = "Garajul bandei",
  player = "Jucător",
  impound = "Depozit",
  inGarage = "În garaj",
  notInGarage = "Nu este în garaj",
  car = "Mașină",
  air = "Aer",
  sea = "Mare",
  fuel = "Combustibil",
  engine = "Motor",
  body = "Caroserie",
  day = "zi",
  days = "zile",
  hour = "oră",
  hours = "ore",
  mins = "minute",

  -- Interfața utilizatorului
  noVehicles = "Nu există vehicule în acest garaj",
  noVehiclesAvailableToDrive = "Nu există vehicule disponibile pentru a conduce",
  vehicles = "vehicul(e)",
  vehiclePlate = "Număr de înmatriculare",
  vehicleNotInGarage = "Vehiculul a fost lăsat afară",
  vehicleTakeOut = "Condu",
  vehicleReturnAndTakeOut = "Întoarce & Condu",
  vehicleReturnToOwnersGarage = "Întoarce la garajul proprietarului",
  transferToGarageOrPlayer = "Transferă la un garaj sau jucător",
  transferToGarage = "Transferă la un garaj",
  transferToPlayer = "Transferă la un jucător",
  vehicleTransfer = "Transfer",
  noAvailableGarages = "Nu există garaje disponibile",
  currentGarage = "Garaj curent",
  noPlayersOnline = "Nu sunt jucători online",
  createPrivateGarage = "Creează un garaj privat",
  pgAlertHeadsUp = "Atenție!",
  pgAlertText = "Garajul va fi creat și vehiculele vor apărea exact în locația și direcția în care te afli în prezent.",
  garageName = "Nume Garaj",
  impoundInformation = "Informații despre depozit",
  impoundedBy = "Depozitat de",
  impoundedReason = "Motiv",
  impoundPlayerCanCollect = "Poți să îți recuperezi vehiculul din depozit.",
  impoundCollectionContact = "Te rugăm să contactezi %{value} pentru a-ți recupera vehiculul.",
  impoundNoVehicles = "Nu există vehicule în depozit",
  impoundAvailable = "Disponibil",
  impoundRetrievableByOwner = "Poate fi recuperat de proprietar",
  impoundNoReason = "Niciun motiv furnizat",
  impoundVehicle = "Depozitează vehiculul",
  impoundReasonField = "Motiv (opțional)",
  impoundTime = "Timp de depozitare",
  impoundAvailableImmediately = "Disponibil imediat",
  impoundCost = "Cost",
  changeVehiclePlate = "Schimbă numărul de înmatriculare",
  newPlate = "Număr nou",
  search = "Caută după nume sau număr",
  noPrivateGarages = "Nu există garaje private",
  editPrivateGarage = "Editează Garaj Privat",
  owners = "Proprietar(i)",
  location = "Locație",
  next = "Următor",
  previous = "Anterior",
  page = "Pagină",
  of = "din",
  show = "Arată",
  save = "Salvează",
  edit = "Editează",
  delete = "Șterge",
  garageDeleteConfirm = "Ești sigur că vrei să ștergi acest garaj?",
  privGarageSearch = "Caută după nume",
  garageUpdatedSuccess = "Garaj actualizat cu succes!",
  getCurrentCoords = "Obține coordonatele curente",
  identifier = "Identificator",
  name = "Nume",
  noPlayers = "Nu există jucători adăugați",
  addPlayer = "Adaugă jucător",
  loadingVehicle = "Se încarcă vehiculul...",
  vehicleSetup = "Configurare Vehicul",
  extras = "Extraopțiuni",
  extra = "Extraopțiune",
  liveries = "Livree",
  livery = "Livrea",
  noLiveries = "Nu există livree disponibile",
  noExtras = "Nu există extraopțiuni disponibile",
  none = "Niciunul",
  vehicleNeedsService = "Necesită service",
  type = "Tip",
  goInside = "Intră înăuntru",

  -- Notificări
  insertVehicleTypeError = "Poți depozita doar vehicule de tip %{value} în acest garaj",
  insertVehiclePublicError = "Nu poți depozita vehicule de serviciu sau de bandă în garajele publice",
  vehicleParkedSuccess = "Vehicul parcat în garaj",
  vehicleNotOwnedError = "Nu deții acest vehicul",
  vehicleNotOwnedByPlayerError = "Vehiculul nu este deținut de un jucător",
  notEnoughMoneyError = "Nu ai destui bani în bancă",
  vehicleNotYoursError = "Vehiculul nu îți aparține",
  notJobOrGangVehicle = "Acesta nu este un vehicul %{value}",
  invalidGangError = "Nu ai furnizat o bandă validă",
  invalidJobError = "Nu ai furnizat un job valid",
  notInsideVehicleError = "Nu ești într-un vehicul",
  vehicleAddedToGangGarageSuccess = "Vehicul adăugat în garajul bandei %{value}!",
  vehicleAddedToJobGarageSuccess = "Vehicul adăugat în garajul de serviciu %{value}!",
  moveCloserToVehicleError = "Trebuie să te apropii de vehicul",
  noVehiclesNearbyError = "Nu există vehicule în apropiere",
  commandPermissionsError = "Nu ai permisiunea de a folosi această comandă",
  actionNotAllowedError = "Nu ai permisiunea să faci asta",
  garageNameExistsError = "Numele garajului există deja",
  vehiclePlateExistsError = "Numărul de înmatriculare este deja utilizat",
  playerNotOnlineError = "Jucătorul nu este online",
  vehicleTransferSuccess = "Vehicul transferat la %{value}",
  vehicleTransferSuccessGeneral = "Vehicul transferat cu succes",
  vehicleReceived = "Ai primit un vehicul cu numărul %{value}",
  vehicleImpoundSuccess = "Vehicul depozitat cu succes",
  vehicleImpoundRemoveSuccess = "Vehicul eliminat din depozit",
  vehicleImpoundReturnedToOwnerSuccess = "Vehiculul a fost returnat în garajul proprietarului",
  garageCreatedSuccess = "Garaj creat cu succes!",
  vehiclePlateUpdateSuccess = "Numărul de înmatriculare a fost schimbat în %{value}",
  vehicleDeletedSuccess = "Vehicul șters din baza de date %{value}",
  playerIsDead = "Nu poți face asta când ești mort",
  vehicleStoreError = "Nu poți depozita acest vehicul aici",

  -- Comenzi
  cmdSetGangVehicle = "Adaugă vehiculul curent în garajul bandei",
  cmdRemoveGangVehicle = "Setează vehiculul bandei înapoi ca deținut de jucător",
  cmdSetJobVehicle = "Adaugă vehiculul curent în garajul de serviciu",
  cmdRemoveJobVehicle = "Setează vehiculul de serviciu înapoi ca deținut de jucător",
  cmdArgGangName = "Numele bandei",
  cmdArgJobName = "Numele jobului",
  cmgArgMinGangRank = "Rangul minim în bandă",
  cmgArgMinJobRank = "Rangul minim la job",
  cmdArgPlayerId = "ID-ul jucătorului noului proprietar",
  cmdImpoundVehicle = "Depozitează un vehicul",
  cmdChangePlate = "Schimbă numărul de înmatriculare (doar Admin)",
  cmdDeleteVeh = "Șterge vehicul din baza de date (doar Admin)",
  cmdCreatePrivGarage = "Creează un garaj privat pentru un jucător",
}
