Locales = Locales or {}

Locales['fi'] = {
  yes = "Kyllä",
  no = "Ei",
  garage = "Autotalli",
  jobGarage = "Työn Autotalli",
  gangGarage = "Jengin Autotalli",
  player = "Pelaaja",
  impound = "Varik<PERSON>",
  inGarage = "Autotallissa",
  notInGarage = "Ulkona tallista",
  car = "Maa Ajoneuvo",
  air = "Ilma Ajoneuvo",
  sea = "Vesi Ajoneuvo",
  fuel = "Bensa",
  engine = "Moottori",
  body = "Keho",
  day = "päivä",
  days = "päivät",
  hour = "tunti",
  hours = "tunnit",

  -- User Interface
  noVehicles = "Ei ajoneuvoja tallissa",
  vehicles = "Ajoneuvot",
  vehiclePlate = "Rekisterikilpi",
  vehicleNotInGarage = "Ajoneuvo ei ole tallissa",
  vehicleTakeOut = "Aja",
  vehicleReturnAndTakeOut = "Palauta talliin ja aja",
  vehicleReturnToOwnersGarage = "Palauta omistajan talliin",
  transferToGarageOrPlayer = "Siirrä autotalliin tai pelaajalle",
  transferToGarage = "Siirrä autotalliin",
  transferToPlayer = "Siirrä pelaajalle",
  vehicleTransfer = "Siirrä",
  noAvailableGarages = "Ei vapaita autotalleja",
  currentGarage = "Tämän hetkinen talli",
  noPlayersOnline = "Ei pelaajia kaupungissa",
  createPrivateGarage = "Luo yksityinen autotalli",
  pgAlertHeadsUp = "Heads up!",
  pgAlertText = "The garage will be created and vehicles will spawn in the exact location and direction are you are currently standing.",
  garageName = "Autotallin nimi",
  impoundInformation = "Varkki tiedot",
  impoundedBy = "Varikoijja",
  impoundedReason = "Syy",
  impoundPlayerCanCollect = "Voit hakea ajoneuvosi varikolta.",
  impoundCollectionContact = "Ota yhteyttä %{value} saadaksesi ajoneuvon takaisin.",
  impoundNoVehicles = "Ei ajoneivoja varikolla",
  impoundAvailable = "Vapaa",
  impoundRetrievableByOwner = "Omistaja voi hakea",
  impoundNoReason = "Syytä ei ole annettu",
  impoundVehicle = "Takavarikoi ajoneuvo",
  impoundReasonField = "Syy",
  impoundTime = "Varikoinnin pituus",
  impoundAvailableImmediately = "Saatavilla heti",
  impoundCost = "Hinta",
  changeVehiclePlate = "Vaihda ajoneuvon kilpeä",
  newPlate = "Uusi kilpi",
  search = "Hae nimellä tai ajoneuvokilven mukaan",
  noPrivateGarages = "Ei yksityisiä autotalleja",
  editPrivateGarage = "Edit Yksityinen autotalli",
  owners = "Omistaja(t)",
  location = "Sijainti",
  next = "Seuraava",
  previous = "Edellinen",
  page = "Sivu",
  of = "of",
  show = "Näytä",
  save = "Tallenna",
  edit = "Muokkaa",
  delete = "Poista",
  garageDeleteConfirm = "Haluatko varmasti poistaa tämän autotallin?",
  privGarageSearch = "Haku nimen mukaan",
  garageUpdatedSuccess = "Autotalli onnistuneesti päivitetty!",
  getCurrentCoords = "Hae nykyiset koordinaatit",
  identifier = "Tunniste",
  name = "Nimi",
  noPlayers = "Pelaajia ei ole lisätty",
  addPlayer = "Lisää pelaaja",
  loadingVehicle = "Ajoneuvon lastaus...",
  vehicleSetup = "Ajoneuvon asennus",
  extras = "Lisävarusteet",
  extra = "Extra",
  liveries = "maalaukset",
  livery = "maksullisuus",
  noLiveries = "Maalauksia ei ole saatavilla",
  noExtras = "Ei lisävarusteita saatavilla",
  none = "Ei ole",
  vehicleNeedsService = "Needs Service",
  type = "Type",

  -- Notifications
  insertVehicleTypeError = "Voit ainoastaan tallettaa %{value} ajoneuvo tyyppejä tähän talliin",
  insertVehiclePublicError = "Et voi tallettaa työn ajoneuvoja julkisiin talleihin",
  vehicleParkedSuccess = "Ajoneuvo parkkeerattu talliin",
  vehicleNotOwnedError = "Et omista tätä autoa",
  vehicleNotOwnedByPlayerError = "Pelaaja ei omista tätä autoa",
  notEnoughMoneyError = "Ei tarpeeksi rahaa pankissa",
  vehicleNotYoursError = "Et omista ajoneuvoa",
  notJobOrGangVehicle = "Tämä ei ole %{value} ajoneuvo",
  invalidGangError = "You have not provided a valid gang",
  invalidJobError = "You have not provided a valid job",
  notInsideVehicleError = "Et ole ajoneuvossa",
  vehicleAddedToGangGarageSuccess = "Vehicle added to the %{value} gang garage!",
  vehicleAddedToJobGarageSuccess = "Vehicle added to the %{value} job garage!",
  moveCloserToVehicleError = "Mene lähemmäs ajoneuvoa",
  noVehiclesNearbyError = "Ei autoja lähellä",
  commandPermissionsError = "Et saa käyttää tätä komentoa",
  actionNotAllowedError = "Et saa tehdä tätä",
  garageNameExistsError = "Tallin nimi on jo olemassa",
  vehiclePlateExistsError = "Ajoneuvon kilpi on jo käytössä",
  playerNotOnlineError = "Pelaaja ei ole kaupungissa",
  vehicleTransferSuccess = "Ajoneuvo on siirretty: %{value}",
  vehicleTransferSuccessGeneral = "Ajoneuvo on onnistuneesti siirretty",
  vehicleReceived = "Sait ajoneuvon kilvellä %{value}",
  vehicleImpoundSuccess = "Ajoneuvo on onnistuneesti Takavarikoitu",
  vehicleImpoundRemoveSuccess = "Ajoneuvo on poistettu takavatikolta",
  vehicleImpoundReturnedToOwnerSuccess = "Ajoneuvo on palautettu omistajan talliin",
  garageCreatedSuccess = "Autotalli onnistuneesti luotu!",
  vehiclePlateUpdateSuccess = "Ajoneuvon kilpi on laitettu %{value}",
  vehicleDeletedSuccess = "Ajoneuvo on poistettu databasesta %{value}",
  playerIsDead = "Et voi tehdä tätä, kun olet kuollut",

  -- Commands
  cmdSetGangVehicle = "Add current vehicle to a gang garage",
  cmdRemoveGangVehicle = "Set gang vehicle back to player owned",
  cmdSetJobVehicle = "Add current vehicle to a job garage",
  cmdRemoveJobVehicle = "Set job vehicle back to player owned",
  cmdArgGangName = "Gang name",
  cmdArgJobName = "Job name",
  cmgArgMinGangRank = "Minimum gang rank",
  cmgArgMinJobRank = "Minimum job rank",
  cmdArgPlayerId = "Player ID of new owner",
  cmdImpoundVehicle = "Impound a vehicle",
  cmdChangePlate = "Change vehicle plate (Admin only)",
  cmdDeleteVeh = "Delete vehicle from database (Admin only)",
  cmdCreatePrivGarage = "Create a private garage for a player",

  -- v3
  vehicleStoreError = "You cannot store this vehicle here",
  mins = "mins",
  noVehiclesAvailableToDrive = "There are no vehicles available to drive",
}
