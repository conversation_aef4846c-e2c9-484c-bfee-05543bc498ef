Locales = Locales or {}

Locales['fr'] = {
  yes = "Oui",
  no = "Non",
  garage = "Garage",
  jobGarage = "Garage métier",
  gangGarage = "Garage Gang",
  player = "Joueurs",
  impound = "Fourriere",
  inGarage = "Dans le garage",
  notInGarage = "Pas dans le garage",
  car = "Voiture",
  air = "Air",
  sea = "Mer",
  fuel = "Essence",
  engine = "Moteur",
  body = "Carroserie",
  day = "Jour",
  days = "Jours",
  hour = "Heure",
  hours = "Heures",
  mins = "mins",

  -- User Interface
  noVehicles = "Il n'y a aucun véhicule dans ce garage",
  noVehiclesAvailableToDrive = "Aucun véhicule n'est disponible pour conduire",
  vehicles = "Voiture(s)",
  vehiclePlate = "Plaque du véhicule",
  vehicleNotInGarage = "Le véhicule a été laissé de côté",
  vehicleTakeOut = "Conduire",
  vehicleReturnAndTakeOut = "Retour et conduite",
  vehicleReturnToOwnersGarage = "Retour au garage du propriétaire",
  transferToGarageOrPlayer = "Transfert vers un garage ou un joueur",
  transferToGarage = "Transfert vers un garage",
  transferToPlayer = "Transférer vers un joueur",
  vehicleTransfer = "Transfert",
  noAvailableGarages = "Aucun garage disponible",
  currentGarage = "Garage actuel",
  noPlayersOnline = "Aucun joueur en ligne",
  createPrivateGarage = "Créer un garage privé",
  pgAlertHeadsUp = "Attention !",
  pgAlertText = "Le garage sera créé et les véhicules apparaîtront à l'emplacement et dans la direction exacts où vous vous trouvez actuellement.",
  garageName = "Nom du garage",
  impoundInformation = "Informations de mise en fourrière",
  impoundedBy = "Mise en fourrière par",
  impoundedReason = "Raison",
  impoundPlayerCanCollect = "Vous pouvez récupérer votre véhicule à la fourrière.",
  impoundCollectionContact = "Veuillez contacter %{value} afin de récupérer votre véhicule.",
  impoundNoVehicles = "Il n'y a aucun véhicule dans la fourrière",
  impoundAvailable = "Disponible",
  impoundRetrievableByOwner = "Récupérable par le propriétaire",
  impoundNoReason = "Aucune raison fournie",
  impoundVehicle = "Véhicule mis en fourrière",
  impoundReasonField = "Raison (facultatif)",
  impoundTime = "Durée de mise en fourrière",
  impoundAvailableImmediately = "Disponible immédiatement",
  impoundCost = "Coût",
  changeVehiclePlate = "Changer la plaque du véhicule",
  newPlate = "Nouvelle plaque",
  search = "Recherche par nom ou plaque",
  noPrivateGarages = "Pas de garages privés",
  editPrivateGarage = "Modifier le garage privé",
  owners = "Propriétaire(s)",
  location = "Location",
  next = "Suivant",
  previous = "Precedent",
  page = "Page",
  of = "de",
  show = "Montrer",
  save = "Sauvegarde",
  edit = "Editer",
  delete = "Supprimer",
  garageDeleteConfirm = "Etes-vous sûr de vouloir supprimer ce garage ?",
  privGarageSearch = "Rechercher par nom",
  garageUpdatedSuccess = "Garage mis à jour avec succès !",
  getCurrentCoords = "Obtenir les coordonnées actuelles",
  identifier = "Identifiant",
  name = "Nom",
  noPlayers = "Aucun joueur n'a été ajouté",
  addPlayer = "Ajouter un joueur",
  chargementVehicle = "Chargement du véhicule...",
  vehicleSetup = "Configuration du véhicule",
  extras = "Suppléments",
  extra = "Supplément",
  liveries = "Livrées",
  livery = "Livrée",
  noLiveries = "Aucune livrée disponible",
  noExtras = "Aucun extra disponible",
  none = "Aucun(e)",
  vehicleNeedsService = "Besoin de services",
  type = "Genre",
  goInside = "Aller à l'intérieur",

  -- Notifications
  insertVehicleTypeError = "Vous ne pouvez stocker que %{value} types de véhicules dans ce garage",
  insertVehiclePublicError = "Vous ne pouvez pas stocker de véhicules de travail ou de gang dans les garages publics",
  vehicleParkedSuccess = "Véhicule garé dans le garage",
  vehicleNotOwnedError = "Vous n'êtes pas propriétaire de ce véhicule",
  vehicleNotOwnedByPlayerError = "Le véhicule n'appartient à aucun joueur",
  notEnoughMoneyError = "Vous n'avez pas assez d'argent en banque",
  vehicleNotYoursError = "Le véhicule ne vous appartient pas",
  notJobOrGangVehicle = "Ceci n'est pas un véhicule %{value}",
  invalidGangError = "Vous n'avez pas fourni de gang valide",
  invalidJobError = "Vous n'avez pas fourni d'emploi valide",
  notInsideVehicleError = "Vous n'êtes pas assis dans un véhicule",
  vehicleAddedToGangGarageSuccess = "Véhicule ajouté au garage du gang %{value} !",
  vehicleAddedToJobGarageSuccess = "Véhicule ajouté au garage de travail %{value} !",
  moveCloserToVehicleError = "Vous devez vous rapprocher du véhicule",
  noVehiclesNearbyError = "Il n'y a aucun véhicule à proximité",
  commandPermissionsError = "Vous n'êtes pas autorisé à utiliser cette commande",
  actionNotAllowedError = "Vous n'êtes pas autorisé à faire cela",
  garageNameExistsError = "Le nom du garage existe déjà",
  VehiclePlateExistsError = "La plaque du véhicule est déjà utilisée",
  playerNotOnlineError = "Le joueur n'est pas en ligne",
  vehicleTransferSuccess = "Véhicule transféré à %{value}",
  vehicleTransferSuccessGeneral = "Véhicule transféré avec succès",
  vehicleReceived = "Vous avez reçu un véhicule avec la plaque %{value}",
  vehicleImpoundSuccess = "Véhicule mis en fourrière avec succès",
  vehicleImpoundRemoveSuccess = "Véhicule retiré de la fourrière",
  vehicleImpoundReturnedToOwnerSuccess = "Véhicule restitué au garage du propriétaire",
  garageCreatedSuccess = "Garage créé avec succès !",
  vehiclePlateUpdateSuccess = "Plaque du véhicule réglée sur %{value}",
  vehicleDeletedSuccess = "Véhicule supprimé de la base de données %{value}",
  playerIsDead = "Vous ne pouvez pas faire ça tant que vous êtes mort",
  vehicleStoreError = "Vous ne pouvez pas stocker ce véhicule ici",

  -- Commands
  cmdSetGangVehicle = "Ajouter le véhicule actuel à un garage collectif",
  cmdRemoveGangVehicle = "Remettre le véhicule du gang comme appartenant au joueur",
  cmdSetJobVehicle = "Ajouter le véhicule actuel à un garage",
  cmdRemoveJobVehicle = "Remettre le véhicule de travail comme appartenant au joueur",
  cmdArgGangName = "Nom du gang",
  cmdArgJobName = "Nom du travail",
  cmgArgMinGangRank = "Rang minimum du gang",
  cmgArgMinJobRank = "Classement minimum du poste",
  cmdArgPlayerId = "ID du joueur du nouveau propriétaire",
  cmdImpoundVehicle = "Mise en fourrière d'un véhicule",
  cmdChangePlate = "Changer la plaque du véhicule (Administrateur uniquement)",
  cmdDeleteVeh = "Supprimer le véhicule de la base de données (Administrateur uniquement)",
  cmdCreatePrivGarage = "Créer un garage privé pour un joueur",
}
