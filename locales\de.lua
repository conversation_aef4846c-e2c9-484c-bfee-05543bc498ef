Locales = Locales or {}

Locales['de'] = {
  yes = "Ja",
  no = "Nein",
  garage = "Garage",
  jobGarage = "Jobgarage",
  gangGarage = "Ganggarage",
  player = "Spieler",
  impound = "Impound",
  inGarage = "In der Garage",
  notInGarage = "Nicht in der Garage",
  car = "Auto",
  air = "Flugzeug",
  sea = "Boot",
  fuel = "Tank",
  engine = "Motor",
  body = "Schaden",
  day = "Tag",
  days = "Tage",
  hour = "Stunde",
  hours = "Stunden",

  -- User Interface
  noVehicles = "Keine Fahrzeuge in der Garage",
  vehicles = "Fahrzeug(e)",
  vehiclePlate = "Kennzeichen",
  vehicleNotInGarage = "Nicht in der Garage",
  vehicleTakeOut = "Ausparken",
  vehicleReturnAndTakeOut = "Einparken und Ausparken",
  vehicleReturnToOwnersGarage = "Zurück in die Privatgarage",
  transferToGarageOrPlayer = "Transfer in eine andere Garage oder an einen Spieler",
  transferToGarage = "Transfer in eine andere Garage",
  transferToPlayer = "Transfer zu einem Spieler",
  vehicleTransfer = "Transfer",
  noAvailableGarages = "Keine Verfügbaren Garagen",
  currentGarage = "Aktuelle Garage",
  noPlayersOnline = "Kein Spieler online",
  createPrivateGarage = "Private Garage erstellen",
  pgAlertHeadsUp = "Kopf hoch!",
  pgAlertText = "Garage wird erstellt und Fahrzeuge spawnen an der Stelle und in die Richtung die du schaust",
  garageName = "Garagenname",
  impoundInformation = "Impound Information",
  impoundedBy = "Beschlagnahmt von",
  impoundedReason = "Grund",
  impoundPlayerCanCollect = "Fahrzeug kann am Impound ausgelöst werden",
  impoundCollectionContact = "Kontaktiere %{value} um dein Fahrzeug auszulösen",
  impoundNoVehicles = "Keine Fahrzeuge im Impound",
  impoundAvailable = "Verfügbar",
  impoundRetrievableByOwner = "Kann vom Eigentümer ausgelöst werden",
  impoundNoReason = "Kein Grund angegeben",
  impoundVehicle = "Fahrzeug beschlagnahmen",
  impoundReasonField = "Grund (optional)",
  impoundTime = "Impound Zeit",
  impoundAvailableImmediately = "Sofort verfügbar",
  impoundCost = "Kosten",
  changeVehiclePlate = "Kennzeichen ändern",
  newPlate = "Neues Kennzeichen",
  search = "Suche nach Name oder Kennzeichen",
  noPrivateGarages = "Keine privaten Garagen",
  editPrivateGarage = "Private Garage bearbeiten",
  owners = "Eigentümer(n)",
  location = "Standort",
  next = "Weiter",
  previous = "Vorherige",
  page = "Seite",
  of = "von",
  show = "Anzeigen",
  save = "Speichern Sie",
  edit = "Bearbeiten",
  delete = "Löschen",
  garageDeleteConfirm = "Sind Sie sicher, dass Sie diese Garage löschen wollen?",
  privGarageSearch = "Suche nach Name",
  garageUpdatedSuccess = "Garage erfolgreich aktualisiert!",
  getCurrentCoords = "Aktuelle Koordinaten abrufen",
  identifier = "Kennung",
  name = "Name",
  noPlayers = "Es sind keine Spieler hinzugefügt worden",
  addPlayer = "Spieler hinzufügen",
  loadingVehicle = "Fahrzeug laden...",
  vehicleSetup = "Fahrzeug-Setup",
  extras = "Extras",
  extra = "Extra",
  liveries = "Livreen",
  livery = "Leihwagen",
  noLiveries = "Keine Lackierungen verfügbar",
  noExtras = "Keine Extras verfügbar",
  none = "Keine",
  vehicleNeedsService = "Needs Service",
  type = "Type",

  -- Notifications
  insertVehicleTypeError = "Du kannst hier nur %{value} einparken",
  insertVehiclePublicError = "Du kannst keine Jobfahrzeuge in Öffentliche Garagen einparken",
  vehicleParkedSuccess = "Erfolgreich eingeparkt",
  vehicleNotOwnedError = "Du hast keinen Schlüssel",
  vehicleNotOwnedByPlayerError = "Kein registriertes Fahrzeug",
  notEnoughMoneyError = "Nicht genug Geld auf dem Konto",
  vehicleNotYoursError = "Fahrzeug gehört nicht dir",
  notJobOrGangVehicle = "Kein %{value} Fahrzeug",
  invalidGangError = "Keine gültige Gang",
  invalidJobError = "Kein gültiger Job",
  notInsideVehicleError = "Du sitzt nicht in einem Fahrzeug",
  vehicleAddedToGangGarageSuccess = "Fahrzeug zur %{value} Ganggarage hinzugefügt",
  vehicleAddedToJobGarageSuccess = "Fahrzeug zur %{value} Jobgarage hinzugefügt",
  moveCloserToVehicleError = "Du musst näher ans Fahrzeug",
  noVehiclesNearbyError = "Kein Fahrzeug in der nähe",
  commandPermissionsError = "Keine Berechtigung für diesen Command",
  actionNotAllowedError = "Keine Berechtigung",
  garageNameExistsError = "Garagenname exestiert bereites",
  vehiclePlateExistsError = "Kennzeichen exestiert bereites",
  playerNotOnlineError = "Spieler ist offline",
  vehicleTransferSuccess = "Fahrzeug an %{value} übertragen",
  vehicleTransferSuccessGeneral = "Fahrzeug erfolgreich übertragen",
  vehicleReceived = "Fahrzeug mit dem Kennzeichen %{value} erhalten",
  vehicleImpoundSuccess = "Fahrzeug erfolgreich beschlagnahmt",
  vehicleImpoundRemoveSuccess = "Fahrzeug wurde aus dem Impound ausgelöst",
  vehicleImpoundReturnedToOwnerSuccess = "Fahrzeug zurück in der Eigentümergarage",
  garageCreatedSuccess = "Garage erfolgreich erstellt",
  vehiclePlateUpdateSuccess = "Kennzeichen wurde zu %{value} geändert",
  vehicleDeletedSuccess = "Fahrzeug aus der Datenbank %{value} gelöscht",
  playerIsDead = "Sie können dies nicht tun, während Sie tot sind",

  -- Commands
  cmdSetGangVehicle = "Aktuelles Fahrzeug einer Ganggarage hinzufügen",
  cmdRemoveGangVehicle = "Setze das Gang-Fahrzeug wieder in den Besitz des Spielers",
  cmdSetJobVehicle = "Aktuelles Fahrzeug einer Jobgarage hinzufügen",
  cmdRemoveJobVehicle = "Setze das Job-Fahrzeug wieder in den Besitz des Spielers",
  cmdArgGangName = "Gangname",
  cmdArgJobName = "Jobname",
  cmgArgMinGangRank = "Minimaler Gangrang",
  cmgArgMinJobRank = "Mindestjobrang",
  cmdArgPlayerId = "Spieler ID vom Eigentümer",
  cmdImpoundVehicle = "Fahrzeug beschlagnahmen",
  cmdChangePlate = "Kennzeichen ändern (Admin only)",
  cmdDeleteVeh = "Fahrzeug aus der Datenbank löschen (Admin only)",
  cmdCreatePrivGarage = "Private Garage erstellen",

  -- v3
  vehicleStoreError = "You cannot store this vehicle here",
  mins = "mins",
  noVehiclesAvailableToDrive = "There are no vehicles available to drive",
}
